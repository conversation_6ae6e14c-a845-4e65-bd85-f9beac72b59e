/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background-color: #ffffff;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3.5rem;
    font-weight: 800;
}

h2 {
    font-size: 2.5rem;
    font-weight: 700;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
    color: #6b7280;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
}

.btn-secondary {
    background: white;
    color: #4f46e5;
    border: 2px solid #4f46e5;
}

.btn-secondary:hover {
    background: #4f46e5;
    color: white;
}

.btn-outline {
    background: transparent;
    color: #4f46e5;
    border: 2px solid #4f46e5;
}

.btn-outline:hover {
    background: #4f46e5;
    color: white;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: 800;
    color: #1f2937;
    text-decoration: none;
}

.logo-img {
    height: 40px;
    width: auto;
    object-fit: contain;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 30px;
    align-items: center;
}

.nav-links a {
    text-decoration: none;
    color: #6b7280;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: #4f46e5;
}

.demo-btn {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white !important;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
}

.demo-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #4f46e5;
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text h1 {
    margin-bottom: 20px;
    color: #1f2937;
}

.gradient-text {
    background: linear-gradient(135deg, #4f46e5, #7c3aed, #06b6d4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 30px;
    color: #6b7280;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;
}

.hero-stats {
    display: flex;
    gap: 40px;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    color: #4f46e5;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

/* Hero Visual */
.hero-visual {
    position: relative;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-card {
    position: absolute;
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    min-width: 200px;
    animation: float 6s ease-in-out infinite;
}

.card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.card-3 {
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.card-icon {
    font-size: 24px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-text h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #1f2937;
}

.card-text p {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

.hero-center-element {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ai-brain {
    width: 120px;
    height: 120px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.brain-core {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
}

.brain-core::before {
    content: '🧠';
    font-size: 32px;
}

.brain-pulse {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 3px solid #4f46e5;
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    100% { transform: scale(1.5); opacity: 0; }
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    margin-bottom: 20px;
    color: #1f2937;
}

.section-header p {
    font-size: 1.125rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    background: #f8fafc;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text p {
    font-size: 1.125rem;
    margin-bottom: 30px;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 12px;
}

.feature i {
    color: #10b981;
    font-size: 18px;
}

.feature span {
    color: #374151;
    font-weight: 500;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #4f46e5, #7c3aed);
}

.timeline-item {
    position: relative;
    margin-bottom: 40px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    background: #4f46e5;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 3px #4f46e5;
}

.timeline-year {
    font-size: 1.25rem;
    font-weight: 700;
    color: #4f46e5;
    margin-bottom: 5px;
}

.timeline-content {
    color: #6b7280;
    font-weight: 500;
}

/* Products Section */
.products {
    background: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.product-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4f46e5, #7c3aed);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.product-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.product-icon {
    font-size: 48px;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-title h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 5px;
}

.product-title p {
    color: #6b7280;
    font-weight: 500;
    margin: 0;
}

.product-description {
    margin-bottom: 30px;
}

.product-description p {
    font-size: 1.125rem;
    line-height: 1.6;
}

.product-features {
    margin-bottom: 30px;
}

.feature-group {
    margin-bottom: 25px;
}

.feature-group h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 10px;
}

.feature-group ul {
    list-style: none;
    padding: 0;
}

.feature-group li {
    padding: 8px 0;
    padding-left: 20px;
    position: relative;
    color: #6b7280;
}

.feature-group li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

.trusted-by {
    margin-top: 20px;
}

.publisher-logos {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.publisher {
    background: #f3f4f6;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
}

.product-cta {
    display: inline-block;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.product-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
}

/* Specific product card styling */
.product-card.ibookgpt::before {
    background: linear-gradient(90deg, #4f46e5, #7c3aed);
}

.product-card.gptsir::before {
    background: linear-gradient(90deg, #10b981, #059669);
}

.product-card.publisher-whitelabel::before {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

/* Why It Matters Section */
.why-matters {
    background: #f8fafc;
}

.stakeholders-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.stakeholder-card {
    background: white;
    padding: 40px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.stakeholder-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stakeholder-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.stakeholder-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 15px;
}

.stakeholder-card p {
    color: #6b7280;
    line-height: 1.6;
}

/* Integration Section */
.integration {
    background: white;
}

.integration-content {
    max-width: 1000px;
    margin: 0 auto;
}

.integration-benefits {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-bottom: 50px;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 30px;
    background: #f8fafc;
    border-radius: 16px;
    transition: all 0.3s ease;
}

.benefit-item:hover {
    background: #f1f5f9;
    transform: translateY(-2px);
}

.benefit-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.benefit-text h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.benefit-text p {
    color: #6b7280;
    margin: 0;
}

.integration-cta {
    text-align: center;
    padding: 40px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 20px;
    color: white;
}

.integration-cta h3 {
    font-size: 1.75rem;
    margin-bottom: 20px;
    color: white;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.cta-buttons .btn {
    background: white;
    color: #4f46e5;
    border: 2px solid white;
}

.cta-buttons .btn:hover {
    background: transparent;
    color: white;
    border-color: white;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.cta-content {
    text-align: center;
    max-width: 1000px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #1f2937;
}

.cta-content > p {
    font-size: 1.25rem;
    margin-bottom: 50px;
    color: #6b7280;
}

.cta-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.cta-option {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.cta-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.cta-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.cta-option h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 10px;
}

.cta-option p {
    color: #6b7280;
    margin-bottom: 25px;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: 800;
    margin-bottom: 20px;
}

.footer-logo-img {
    height: 50px;
    width: auto;
    object-fit: contain;
    filter: brightness(0) invert(1);
}

.footer-logo .logo-icon {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
}

.footer-description {
    color: #9ca3af;
    margin-bottom: 25px;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #4f46e5;
    transform: translateY(-2px);
}

.footer-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: white;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #4f46e5;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 30px;
    text-align: center;
}

.footer-bottom p {
    color: #9ca3af;
    margin: 0;
}

/* Welcome Banner */
.welcome-banner {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    padding: 15px 25px;
    border-radius: 50px;
    box-shadow: 0 10px 30px rgba(79, 70, 229, 0.3);
    z-index: 1000;
    animation: slideIn 0.5s ease-out;
}

.welcome-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.welcome-icon {
    font-size: 20px;
}

.welcome-text {
    font-weight: 600;
    font-size: 14px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-visual {
        height: 400px;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .products-grid {
        grid-template-columns: 1fr 1fr;
        gap: 25px;
    }

    .stakeholders-grid {
        grid-template-columns: 1fr 1fr;
    }

    .integration-benefits {
        grid-template-columns: 1fr;
    }

    .cta-options {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    .hero {
        padding: 100px 0 60px;
        min-height: auto;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .hero-stats {
        justify-content: center;
        gap: 30px;
    }

    .hero-visual {
        height: 300px;
    }

    .floating-card {
        min-width: 160px;
        padding: 15px;
    }

    .card-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }

    .card-text h4 {
        font-size: 14px;
    }

    .card-text p {
        font-size: 12px;
    }

    .ai-brain {
        width: 100px;
        height: 100px;
    }

    .brain-core {
        width: 70px;
        height: 70px;
    }

    .brain-core::before {
        font-size: 28px;
    }

    section {
        padding: 60px 0;
    }

    .section-header {
        margin-bottom: 40px;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .timeline {
        padding-left: 20px;
    }

    .timeline::before {
        left: 10px;
    }

    .timeline-item::before {
        left: 3px;
        width: 14px;
        height: 14px;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .product-card {
        padding: 30px 20px;
    }

    .product-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .product-icon {
        width: 60px;
        height: 60px;
        font-size: 32px;
    }

    .stakeholders-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .stakeholder-card {
        padding: 30px 20px;
    }

    .integration-benefits {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .benefit-item {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .integration-cta {
        padding: 30px 20px;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .cta-options {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .cta-option {
        padding: 30px 20px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }

    .welcome-banner {
        bottom: 20px;
        right: 20px;
        padding: 12px 20px;
    }

    .welcome-text {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.75rem;
    }

    .hero {
        padding: 90px 0 50px;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-stats {
        gap: 20px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .floating-card {
        min-width: 140px;
        padding: 12px;
    }

    .product-card {
        padding: 25px 15px;
    }

    .stakeholder-card {
        padding: 25px 15px;
    }

    .stakeholder-icon {
        font-size: 36px;
    }

    .benefit-item {
        padding: 15px;
    }

    .benefit-icon {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }

    .integration-cta {
        padding: 25px 15px;
    }

    .cta-option {
        padding: 25px 15px;
    }

    .cta-icon {
        font-size: 36px;
    }

    .welcome-banner {
        bottom: 15px;
        right: 15px;
        padding: 10px 15px;
    }

    .welcome-icon {
        font-size: 16px;
    }

    .welcome-text {
        font-size: 11px;
    }
}
